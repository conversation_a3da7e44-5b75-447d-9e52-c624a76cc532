module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/BubbleChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleChart": (()=>BubbleChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-ssr] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__max$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/max.js [app-ssr] (ecmascript) <export default as max>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__min$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/min.js [app-ssr] (ecmascript) <export default as min>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$pow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__sqrt__as__scaleSqrt$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/pow.js [app-ssr] (ecmascript) <export sqrt as scaleSqrt>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-ssr] (ecmascript) <export default as scaleLinear>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$simulation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceSimulation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/simulation.js [app-ssr] (ecmascript) <export default as forceSimulation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$manyBody$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceManyBody$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/manyBody.js [app-ssr] (ecmascript) <export default as forceManyBody>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$center$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceCenter$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/center.js [app-ssr] (ecmascript) <export default as forceCenter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$collide$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceCollide$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/collide.js [app-ssr] (ecmascript) <export default as forceCollide>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceX$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/x.js [app-ssr] (ecmascript) <export default as forceX>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$y$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceY$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-force/src/y.js [app-ssr] (ecmascript) <export default as forceY>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__color$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-color/src/color.js [app-ssr] (ecmascript) <export default as color>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$drag$2f$src$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__drag$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-drag/src/drag.js [app-ssr] (ecmascript) <export default as drag>");
"use client";
;
;
;
const BubbleChart = ({ data, width = 1200, height = 800, onBubbleHover })=>{
    const svgRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [hoveredBubble, setHoveredBubble] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!svgRef.current || !data.length) return;
        const svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(svgRef.current);
        svg.selectAll("*").remove();
        // Create scales
        const maxMarketCap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__max$3e$__["max"])(data, (d)=>d.marketCap) || 1;
        const minMarketCap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__min$3e$__["min"])(data, (d)=>d.marketCap) || 1;
        // Calculate optimal bubble sizes to fill screen without overlapping
        const totalArea = width * height;
        const bubbleCount = data.length;
        const averageArea = totalArea / bubbleCount * 0.5; // Use 50% of available space for non-overlapping
        const averageRadius = Math.sqrt(averageArea / Math.PI);
        // Radius scale - conservative sizing to prevent overlap
        const radiusScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$pow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__sqrt__as__scaleSqrt$3e$__["scaleSqrt"])().domain([
            minMarketCap,
            maxMarketCap
        ]).range([
            averageRadius * 0.4,
            averageRadius * 1.8
        ]); // More conservative sizing
        // Color scale for price changes with more vibrant colors
        const colorScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain([
            -15,
            -5,
            0,
            5,
            15
        ]).range([
            "#dc2626",
            "#f87171",
            "#6b7280",
            "#34d399",
            "#059669"
        ]).clamp(true);
        // Prepare bubble data
        const bubbleData = data.map((crypto)=>({
                ...crypto,
                x: 0,
                y: 0,
                r: radiusScale(crypto.marketCap),
                color: colorScale(crypto.change24h)
            }));
        // Create force simulation for non-overlapping bubbles like CryptoBubbles
        const simulation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$simulation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceSimulation$3e$__["forceSimulation"])(bubbleData).force("charge", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$manyBody$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceManyBody$3e$__["forceManyBody"])().strength(-50)) // Moderate repulsion to prevent overlap
        .force("center", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$center$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceCenter$3e$__["forceCenter"])(width / 2, height / 2)).force("collision", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$collide$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceCollide$3e$__["forceCollide"])().radius((d)=>d.r + 3) // Small padding to prevent overlap
        .strength(1) // Strong collision detection
        .iterations(3)) // Multiple iterations for better collision resolution
        .force("x", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceX$3e$__["forceX"])(width / 2).strength(0.05)) // Gentle centering
        .force("y", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$force$2f$src$2f$y$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__forceY$3e$__["forceY"])(height / 2).strength(0.05))// Add boundary forces to keep bubbles on screen
        .force("boundary", ()=>{
            bubbleData.forEach((d)=>{
                const padding = d.r + 5;
                d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));
                d.y = Math.max(padding, Math.min(height - padding, d.y || height / 2));
            });
        }).alphaDecay(0.005) // Very slow decay for thorough settling
        .velocityDecay(0.6); // Higher friction for stability
        // Create container group
        const container = svg.append("g");
        // Create bubbles with entrance animation
        const bubbles = container.selectAll(".bubble").data(bubbleData).enter().append("g").attr("class", "bubble").style("cursor", "grab").style("opacity", 0);
        // Add circles with gradient effects and inner glow
        const defs = svg.append("defs");
        // Create enhanced inner glow filter for stroke that glows inward
        const innerGlowFilter = defs.append("filter").attr("id", "inner-glow").attr("x", "-50%").attr("y", "-50%").attr("width", "200%").attr("height", "200%");
        // Create a dilated version of the stroke for the glow base
        innerGlowFilter.append("feMorphology").attr("in", "SourceAlpha").attr("operator", "dilate").attr("radius", "2").attr("result", "dilated");
        // Blur the dilated stroke for glow effect
        innerGlowFilter.append("feGaussianBlur").attr("in", "dilated").attr("stdDeviation", "8").attr("result", "blurred");
        // Create the inner glow by clipping to the circle interior
        innerGlowFilter.append("feComposite").attr("in", "blurred").attr("in2", "SourceAlpha").attr("operator", "in").attr("result", "innerGlow");
        // Combine the original stroke with the inner glow
        const feMerge = innerGlowFilter.append("feMerge");
        feMerge.append("feMergeNode").attr("in", "SourceGraphic");
        feMerge.append("feMergeNode").attr("in", "innerGlow");
        // Create drop shadow filter
        const shadowFilter = defs.append("filter").attr("id", "drop-shadow").attr("x", "-50%").attr("y", "-50%").attr("width", "200%").attr("height", "200%");
        shadowFilter.append("feDropShadow").attr("dx", "2").attr("dy", "2").attr("stdDeviation", "4").attr("flood-color", "rgba(0,0,0,0.4)");
        // Add invisible hover area (larger than visible stroke)
        bubbles.append("circle").attr("class", "hover-area").attr("r", 0).attr("fill", "transparent").attr("stroke", "none").attr("opacity", 0).transition().duration(1000).delay((d, i)=>i * 50).attr("r", (d)=>d.r).on("end", function() {
            if (this.parentNode) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this.parentNode).style("opacity", 1);
            }
        });
        // Main transparent bubble with inner stroke glow only
        bubbles.append("circle").attr("class", "visible-stroke").attr("r", 0).attr("fill", "none") // Completely transparent fill
        .attr("stroke", (d)=>{
            const baseColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__color$3e$__["color"])(d.color);
            return baseColor?.brighter(0.5)?.toString() || d.color;
        }).attr("stroke-width", 3).attr("stroke-opacity", 0.8).attr("opacity", 1).attr("filter", "url(#inner-glow)").attr("pointer-events", "none") // Disable pointer events on stroke
        .transition().duration(1000).delay((d, i)=>i * 50).attr("r", (d)=>d.r);
        // Create improved drag behavior that doesn't interfere with hover
        let isDragging = false;
        let dragStarted = false;
        const dragBehavior = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$drag$2f$src$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__drag$3e$__["drag"])().filter(function(event) {
            // Only allow drag on left mouse button
            return event.button === 0;
        }).clickDistance(8) // Require 8 pixels of movement before drag starts
        .on("start", function(event, d) {
            dragStarted = true;
            isDragging = false; // Will be set to true in drag event
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }).on("drag", function(event, d) {
            if (!isDragging) {
                isDragging = true;
                // Change cursor and visual feedback for drag start
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).style("cursor", "grabbing");
                const circle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).select("circle");
                circle.transition().duration(100).attr("stroke", "#fbbf24").attr("stroke-width", 4).attr("stroke-opacity", 1);
                // Bring to front during drag
                const parent = this.parentNode;
                if (parent) {
                    parent.appendChild(this);
                }
            }
            d.fx = event.x;
            d.fy = event.y;
            // Keep bubble within bounds
            const padding = d.r + 5;
            d.fx = Math.max(padding, Math.min(width - padding, d.fx));
            d.fy = Math.max(padding, Math.min(height - padding, d.fy));
        }).on("end", function(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            if (isDragging) {
                // Reset cursor and visual feedback only if we were actually dragging
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).style("cursor", "grab");
                const circle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).select("circle");
                circle.transition().duration(200).attr("stroke", function() {
                    const baseColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__color$3e$__["color"])(d.color);
                    return baseColor?.brighter(0.5)?.toString() || d.color;
                }).attr("stroke-width", 2).attr("stroke-opacity", 0.7);
            }
            d.fx = null;
            d.fy = null;
            isDragging = false;
            dragStarted = false;
        });
        // Apply improved drag behavior that works with hover
        bubbles.call(dragBehavior);
        // Add symbol text - show for all bubbles with adaptive sizing
        bubbles.append("text").attr("text-anchor", "middle").attr("dy", "-0.1em").attr("font-size", (d)=>Math.max(8, Math.min(d.r * 0.4, 20))).attr("font-weight", "bold").attr("fill", "#ffffff").attr("pointer-events", "none").style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)").style("font-family", "system-ui, -apple-system, sans-serif").style("dominant-baseline", "central").text((d)=>d.symbol);
        // Add percentage text - show for all bubbles
        bubbles.append("text").attr("text-anchor", "middle").attr("dy", "0.8em").attr("font-size", (d)=>Math.max(6, Math.min(d.r * 0.3, 16))).attr("font-weight", "600").attr("fill", "#ffffff").attr("pointer-events", "none").style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)").style("font-family", "system-ui, -apple-system, sans-serif").style("dominant-baseline", "central").text((d)=>`${d.change24h >= 0 ? "+" : ""}${d.change24h.toFixed(2)}%`);
        // Set up hover events that work properly with drag behavior
        bubbles.on("mouseover", function(event, d) {
            // Don't show hover effects while actively dragging
            if (isDragging) return;
            const visibleStroke = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).select(".visible-stroke");
            // Cancel any ongoing transitions to prevent conflicts
            visibleStroke.interrupt();
            // Enhanced hover effect with brighter stroke and increased glow
            visibleStroke.transition().duration(200).attr("stroke-width", 5).attr("stroke", "#fbbf24").attr("stroke-opacity", 1).attr("filter", "url(#inner-glow)");
            // Bring to front by moving to end of parent (proper SVG z-ordering)
            const parent = this.parentNode;
            if (parent) {
                parent.appendChild(this);
            }
            setHoveredBubble(d);
            onBubbleHover?.(d);
        }).on("mouseout", function(event, d) {
            // Don't reset hover effects while actively dragging
            if (isDragging) return;
            const visibleStroke = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(this).select(".visible-stroke");
            // Cancel any ongoing transitions to prevent conflicts
            visibleStroke.interrupt();
            // Reset to original transparent state with stroke only
            visibleStroke.transition().duration(200).attr("stroke-width", 3).attr("stroke", function() {
                const baseColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__color$3e$__["color"])(d.color);
                return baseColor?.brighter(0.5)?.toString() || d.color;
            }).attr("stroke-opacity", 0.8).attr("filter", "url(#inner-glow)");
            setHoveredBubble(null);
            onBubbleHover?.(null);
        });
        // Run simulation for enough iterations to settle bubbles without overlap
        for(let i = 0; i < 500; i++){
            simulation.tick();
        }
        // Update positions on simulation tick without scaling
        simulation.on("tick", ()=>{
            bubbles.attr("transform", (d)=>{
                return `translate(${d.x},${d.y})`;
            });
        });
        // Remove zoom functionality - bubbles should fill the screen
        // Cleanup
        return ()=>{
            simulation.stop();
        };
    }, [
        data,
        width,
        height,
        onBubbleHover
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        "data-oid": "cn3_xst",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                ref: svgRef,
                width: width,
                height: height,
                className: "bg-gradient-to-br from-gray-900 to-gray-800",
                style: {
                    display: "block"
                },
                "data-oid": "vyfe8jc"
            }, void 0, false, {
                fileName: "[project]/src/components/BubbleChart.tsx",
                lineNumber: 378,
                columnNumber: 7
            }, this),
            hoveredBubble && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 border-white/30 z-20 min-w-72 max-w-80 backdrop-blur-md",
                style: {
                    boxShadow: "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)"
                },
                "data-oid": "km3npxp",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3 mb-3",
                        "data-oid": "jc0k_nz",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm",
                                style: {
                                    backgroundColor: hoveredBubble.change24h >= 0 ? "#22c55e" : "#ef4444"
                                },
                                "data-oid": "dejxve:",
                                children: hoveredBubble.symbol.charAt(0)
                            }, void 0, false, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 398,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                "data-oid": "1ok57-r",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "font-bold text-lg text-white",
                                        "data-oid": "wmdqdov",
                                        children: hoveredBubble.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 409,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/80 text-sm font-medium",
                                        "data-oid": "evx3o28",
                                        children: [
                                            "(",
                                            hoveredBubble.symbol,
                                            ") • Rank #",
                                            hoveredBubble.rank
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 412,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 408,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/BubbleChart.tsx",
                        lineNumber: 397,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2 text-sm",
                        "data-oid": "h0nfuhh",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center",
                                "data-oid": "d6yjrk9",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/90 font-medium",
                                        "data-oid": "6m.58cj",
                                        children: "Price:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 426,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-bold text-lg text-white",
                                        "data-oid": "0bj4iet",
                                        children: [
                                            "$",
                                            hoveredBubble.price < 1 ? hoveredBubble.price.toFixed(4) : hoveredBubble.price.toLocaleString()
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 429,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 422,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center",
                                "data-oid": "07x2:ab",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/90 font-medium",
                                        "data-oid": "ot1y:3s",
                                        children: "Market Cap:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 441,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-semibold text-white",
                                        "data-oid": "t5x.htn",
                                        children: hoveredBubble.marketCap >= 1e9 ? `$${(hoveredBubble.marketCap / 1e9).toFixed(2)}B` : `$${(hoveredBubble.marketCap / 1e6).toFixed(2)}M`
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 444,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 437,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center",
                                "data-oid": "hpxbbcg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/90 font-medium",
                                        "data-oid": "4myuzet",
                                        children: "24h Volume:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 455,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-semibold text-white",
                                        "data-oid": "pbh7rf.",
                                        children: hoveredBubble.volume24h >= 1e9 ? `$${(hoveredBubble.volume24h / 1e9).toFixed(2)}B` : `$${(hoveredBubble.volume24h / 1e6).toFixed(2)}M`
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 458,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 451,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center",
                                "data-oid": "rli2gdt",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/90 font-medium",
                                        "data-oid": "_87x9tp",
                                        children: "24h Change:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 469,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${hoveredBubble.change24h >= 0 ? "bg-green-500/20 text-green-300 border border-green-400/30" : "bg-red-500/20 text-red-300 border border-red-400/30"}`,
                                        "data-oid": "wxq.s6:",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                "data-oid": "rdhy:ha",
                                                children: hoveredBubble.change24h >= 0 ? "↗" : "↘"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/BubbleChart.tsx",
                                                lineNumber: 480,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                "data-oid": "f-r8.4q",
                                                children: [
                                                    hoveredBubble.change24h >= 0 ? "+" : "",
                                                    hoveredBubble.change24h.toFixed(2),
                                                    "%"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/BubbleChart.tsx",
                                                lineNumber: 483,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 472,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 465,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center",
                                "data-oid": "a4w:gre",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-white/90 font-medium",
                                        "data-oid": "n46j1tl",
                                        children: "7d Change:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 494,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: `font-semibold ${hoveredBubble.change7d >= 0 ? "text-green-300" : "text-red-300"}`,
                                        "data-oid": "8cu:u_m",
                                        children: [
                                            hoveredBubble.change7d >= 0 ? "+" : "",
                                            hoveredBubble.change7d.toFixed(2),
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/BubbleChart.tsx",
                                        lineNumber: 497,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 490,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "pt-2 border-t border-white/30",
                                "data-oid": "_ul03a5",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-between items-center",
                                    "data-oid": "yrv.:ln",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-white/90 font-medium",
                                            "data-oid": "mwc:cg_",
                                            children: "Category:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/BubbleChart.tsx",
                                            lineNumber: 511,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold",
                                            "data-oid": "y9j.jp4",
                                            children: hoveredBubble.category
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/BubbleChart.tsx",
                                            lineNumber: 514,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/BubbleChart.tsx",
                                    lineNumber: 507,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/BubbleChart.tsx",
                                lineNumber: 506,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/BubbleChart.tsx",
                        lineNumber: 421,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium",
                        "data-oid": "ofbq3_c",
                        children: "Drag to move bubble around"
                    }, void 0, false, {
                        fileName: "[project]/src/components/BubbleChart.tsx",
                        lineNumber: 524,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/BubbleChart.tsx",
                lineNumber: 389,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/BubbleChart.tsx",
        lineNumber: 377,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/LoadingSpinner.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleChartSkeleton": (()=>BubbleChartSkeleton),
    "LoadingSpinner": (()=>LoadingSpinner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
"use client";
;
const LoadingSpinner = ({ size = "md", text = "Loading..." })=>{
    const sizeClasses = {
        sm: "w-4 h-4",
        md: "w-8 h-8",
        lg: "w-12 h-12"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col items-center justify-center p-8",
        "data-oid": "6gmp8xi",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `${sizeClasses[size]} animate-spin rounded-full border-4 border-gray-300 border-t-blue-600`,
                "data-oid": "o81jg6y"
            }, void 0, false, {
                fileName: "[project]/src/components/LoadingSpinner.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            text && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-4 text-gray-600 text-sm",
                "data-oid": "ovy93vh",
                children: text
            }, void 0, false, {
                fileName: "[project]/src/components/LoadingSpinner.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/LoadingSpinner.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
};
const BubbleChartSkeleton = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center",
        "data-oid": "n18j76x",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center",
            "data-oid": "q_ecqx.",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4",
                    "data-oid": "4jdehoc"
                }, void 0, false, {
                    fileName: "[project]/src/components/LoadingSpinner.tsx",
                    lineNumber: 45,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-white text-lg font-medium",
                    "data-oid": ":f8jvvv",
                    children: "Loading cryptocurrency data..."
                }, void 0, false, {
                    fileName: "[project]/src/components/LoadingSpinner.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-400 text-sm mt-2",
                    "data-oid": "h0lsp4m",
                    children: "Preparing interactive bubble chart"
                }, void 0, false, {
                    fileName: "[project]/src/components/LoadingSpinner.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/LoadingSpinner.tsx",
            lineNumber: 44,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/LoadingSpinner.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/ResponsiveBubbleChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ResponsiveBubbleChart": (()=>ResponsiveBubbleChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$BubbleChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/BubbleChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LoadingSpinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/LoadingSpinner.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const ResponsiveBubbleChart = ({ data, onBubbleHover, isLoading = false })=>{
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [dimensions, setDimensions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        width: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 1920,
        height: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 1080
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateDimensions = ()=>{
            // Use full viewport dimensions
            setDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };
        updateDimensions();
        window.addEventListener("resize", updateDimensions);
        return ()=>window.removeEventListener("resize", updateDimensions);
    }, []);
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LoadingSpinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BubbleChartSkeleton"], {}, void 0, false, {
            fileName: "[project]/src/components/ResponsiveBubbleChart.tsx",
            lineNumber: 41,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: containerRef,
        className: "fixed inset-0 w-full h-full",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$BubbleChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BubbleChart"], {
            data: data,
            width: dimensions.width,
            height: dimensions.height,
            onBubbleHover: onBubbleHover
        }, void 0, false, {
            fileName: "[project]/src/components/ResponsiveBubbleChart.tsx",
            lineNumber: 46,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ResponsiveBubbleChart.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/SearchAndFilter.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SearchAndFilter": (()=>SearchAndFilter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/funnel.js [app-ssr] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js [app-ssr] (ecmascript) <export default as TrendingDown>");
"use client";
;
;
const SearchAndFilter = ({ filters, onFiltersChange, categories, compact = false })=>{
    const handleSearchChange = (e)=>{
        onFiltersChange({
            ...filters,
            searchTerm: e.target.value
        });
    };
    const handleCategoryChange = (e)=>{
        onFiltersChange({
            ...filters,
            category: e.target.value === "all" ? undefined : e.target.value
        });
    };
    const handleSortChange = (sortBy)=>{
        const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === "desc" ? "asc" : "desc";
        onFiltersChange({
            ...filters,
            sortBy,
            sortOrder: newSortOrder
        });
    };
    if (compact) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                            className: "absolute left-2 top-1/2 transform -translate-y-1/2 text-white/70 w-3 h-3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 44,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            placeholder: "Search...",
                            value: filters.searchTerm || "",
                            onChange: handleSearchChange,
                            className: "w-full pl-7 pr-3 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 placeholder-white/60 backdrop-blur-sm"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 46,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/SearchAndFilter.tsx",
                    lineNumber: 43,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                    value: filters.category || "all",
                    onChange: handleCategoryChange,
                    className: "w-full px-2 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 backdrop-blur-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            value: "all",
                            children: "All Categories"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, this),
                        categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: category,
                                children: category
                            }, category, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 63,
                                columnNumber: 13
                            }, this))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/SearchAndFilter.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex gap-1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>handleSortChange("marketCap"),
                            className: `px-2 py-1 text-xs font-semibold rounded border transition-colors ${filters.sortBy === "marketCap" ? "bg-blue-500 text-white border-blue-500" : "bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"}`,
                            children: "Cap"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>handleSortChange("change24h"),
                            className: `px-2 py-1 text-xs font-semibold rounded border transition-colors ${filters.sortBy === "change24h" ? "bg-blue-500 text-white border-blue-500" : "bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"}`,
                            children: "24h"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>handleSortChange("price"),
                            className: `px-2 py-1 text-xs font-semibold rounded border transition-colors ${filters.sortBy === "price" ? "bg-blue-500 text-white border-blue-500" : "bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"}`,
                            children: "Price"
                        }, void 0, false, {
                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                            lineNumber: 93,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/SearchAndFilter.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/SearchAndFilter.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white p-6 rounded-lg shadow-lg border mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col lg:flex-row gap-4 items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative flex-1 min-w-64",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                placeholder: "Search cryptocurrencies...",
                                value: filters.searchTerm || "",
                                onChange: handleSearchChange,
                                className: "w-full pl-10 pr-4 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 115,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                        lineNumber: 112,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$funnel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                className: "text-gray-600 w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 126,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                value: filters.category || "all",
                                onChange: handleCategoryChange,
                                className: "px-3 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "all",
                                        children: "All Categories"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 132,
                                        columnNumber: 13
                                    }, this),
                                    categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: category,
                                            children: category
                                        }, category, false, {
                                            fileName: "[project]/src/components/SearchAndFilter.tsx",
                                            lineNumber: 134,
                                            columnNumber: 15
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 127,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                        lineNumber: 125,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleSortChange("marketCap"),
                                className: `px-3 py-2 font-semibold rounded-lg border transition-colors ${filters.sortBy === "marketCap" ? "bg-blue-500 text-white border-blue-500" : "bg-white text-black border-gray-300 hover:bg-gray-50"}`,
                                children: [
                                    "Market Cap",
                                    filters.sortBy === "marketCap" && (filters.sortOrder === "desc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 154,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleSortChange("change24h"),
                                className: `px-3 py-2 font-semibold rounded-lg border transition-colors ${filters.sortBy === "change24h" ? "bg-blue-500 text-white border-blue-500" : "bg-white text-black border-gray-300 hover:bg-gray-50"}`,
                                children: [
                                    "24h Change",
                                    filters.sortBy === "change24h" && (filters.sortOrder === "desc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 171,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 173,
                                        columnNumber: 17
                                    }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 160,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleSortChange("price"),
                                className: `px-3 py-2 font-semibold rounded-lg border transition-colors ${filters.sortBy === "price" ? "bg-blue-500 text-white border-blue-500" : "bg-white text-black border-gray-300 hover:bg-gray-50"}`,
                                children: [
                                    "Price",
                                    filters.sortBy === "price" && (filters.sortOrder === "desc" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 188,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "inline w-4 h-4 ml-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                                        lineNumber: 190,
                                        columnNumber: 17
                                    }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 177,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SearchAndFilter.tsx",
                lineNumber: 110,
                columnNumber: 7
            }, this),
            (filters.searchTerm || filters.category) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 flex flex-wrap gap-2",
                children: [
                    filters.searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "px-3 py-1 bg-blue-100 text-blue-900 rounded-full text-sm font-semibold",
                        children: [
                            "Search: “",
                            filters.searchTerm,
                            "”",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>onFiltersChange({
                                        ...filters,
                                        searchTerm: undefined
                                    }),
                                className: "ml-2 text-blue-700 hover:text-blue-900 font-bold",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 202,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                        lineNumber: 200,
                        columnNumber: 13
                    }, this),
                    filters.category && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "px-3 py-1 bg-green-100 text-green-900 rounded-full text-sm font-semibold",
                        children: [
                            "Category: ",
                            filters.category,
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>onFiltersChange({
                                        ...filters,
                                        category: undefined
                                    }),
                                className: "ml-2 text-green-700 hover:text-green-900 font-bold",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/components/SearchAndFilter.tsx",
                                lineNumber: 215,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/SearchAndFilter.tsx",
                        lineNumber: 213,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/SearchAndFilter.tsx",
                lineNumber: 198,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/SearchAndFilter.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/data/mockCryptoData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateRandomCrypto": (()=>generateRandomCrypto),
    "getAllCryptoData": (()=>getAllCryptoData),
    "mockCryptoData": (()=>mockCryptoData)
});
const mockCryptoData = [
    {
        id: "bitcoin",
        name: "Bitcoin",
        symbol: "BTC",
        price: 43250.75,
        marketCap: 847500000000,
        volume24h: 28500000000,
        change24h: 2.45,
        change7d: -1.23,
        rank: 1,
        category: "Layer 1",
        description: "The first and largest cryptocurrency by market cap"
    },
    {
        id: "ethereum",
        name: "Ethereum",
        symbol: "ETH",
        price: 2650.30,
        marketCap: 318750000000,
        volume24h: 15200000000,
        change24h: 3.78,
        change7d: 5.42,
        rank: 2,
        category: "Smart Contract Platform",
        description: "Decentralized platform for smart contracts and DApps"
    },
    {
        id: "tether",
        name: "Tether",
        symbol: "USDT",
        price: 1.00,
        marketCap: 91800000000,
        volume24h: 45600000000,
        change24h: 0.02,
        change7d: -0.01,
        rank: 3,
        category: "Stablecoin",
        description: "USD-pegged stablecoin"
    },
    {
        id: "bnb",
        name: "BNB",
        symbol: "BNB",
        price: 315.80,
        marketCap: 47370000000,
        volume24h: 1850000000,
        change24h: -1.25,
        change7d: 8.90,
        rank: 4,
        category: "Exchange Token",
        description: "Binance ecosystem token"
    },
    {
        id: "solana",
        name: "Solana",
        symbol: "SOL",
        price: 98.45,
        marketCap: 43200000000,
        volume24h: 2100000000,
        change24h: 5.67,
        change7d: 12.34,
        rank: 5,
        category: "Layer 1",
        description: "High-performance blockchain for DeFi and Web3"
    },
    {
        id: "usdc",
        name: "USD Coin",
        symbol: "USDC",
        price: 1.00,
        marketCap: 32500000000,
        volume24h: 5800000000,
        change24h: 0.01,
        change7d: 0.00,
        rank: 6,
        category: "Stablecoin",
        description: "Regulated USD-backed stablecoin"
    },
    {
        id: "xrp",
        name: "XRP",
        symbol: "XRP",
        price: 0.62,
        marketCap: 33800000000,
        volume24h: 1200000000,
        change24h: -2.15,
        change7d: -5.67,
        rank: 7,
        category: "Payment",
        description: "Digital payment protocol for financial institutions"
    },
    {
        id: "cardano",
        name: "Cardano",
        symbol: "ADA",
        price: 0.48,
        marketCap: 16900000000,
        volume24h: 450000000,
        change24h: 1.89,
        change7d: 3.45,
        rank: 8,
        category: "Layer 1",
        description: "Proof-of-stake blockchain platform"
    },
    {
        id: "avalanche",
        name: "Avalanche",
        symbol: "AVAX",
        price: 36.75,
        marketCap: 14200000000,
        volume24h: 680000000,
        change24h: 4.23,
        change7d: 15.67,
        rank: 9,
        category: "Layer 1",
        description: "Platform for decentralized applications and custom blockchain networks"
    },
    {
        id: "dogecoin",
        name: "Dogecoin",
        symbol: "DOGE",
        price: 0.085,
        marketCap: 12100000000,
        volume24h: 890000000,
        change24h: -3.45,
        change7d: 2.10,
        rank: 10,
        category: "Meme",
        description: "The original meme cryptocurrency"
    },
    {
        id: "chainlink",
        name: "Chainlink",
        symbol: "LINK",
        price: 14.85,
        marketCap: 8750000000,
        volume24h: 420000000,
        change24h: 2.67,
        change7d: 8.90,
        rank: 11,
        category: "Oracle",
        description: "Decentralized oracle network"
    },
    {
        id: "polygon",
        name: "Polygon",
        symbol: "MATIC",
        price: 0.89,
        marketCap: 8200000000,
        volume24h: 380000000,
        change24h: 6.78,
        change7d: 18.45,
        rank: 12,
        category: "Layer 2",
        description: "Ethereum scaling solution"
    },
    {
        id: "litecoin",
        name: "Litecoin",
        symbol: "LTC",
        price: 72.30,
        marketCap: 5400000000,
        volume24h: 320000000,
        change24h: -1.56,
        change7d: 4.23,
        rank: 13,
        category: "Payment",
        description: "Peer-to-peer cryptocurrency based on Bitcoin"
    },
    {
        id: "uniswap",
        name: "Uniswap",
        symbol: "UNI",
        price: 6.45,
        marketCap: 4850000000,
        volume24h: 180000000,
        change24h: 3.89,
        change7d: 12.67,
        rank: 14,
        category: "DeFi",
        description: "Decentralized exchange protocol"
    },
    {
        id: "internet-computer",
        name: "Internet Computer",
        symbol: "ICP",
        price: 12.75,
        marketCap: 5900000000,
        volume24h: 95000000,
        change24h: -2.34,
        change7d: 7.89,
        rank: 15,
        category: "Layer 1",
        description: "Blockchain computer that scales smart contract computation"
    }
];
// Simple seeded random number generator for consistent results
class SeededRandom {
    seed;
    constructor(seed){
        this.seed = seed;
    }
    next() {
        this.seed = (this.seed * 9301 + 49297) % 233280;
        return this.seed / 233280;
    }
}
const generateRandomCrypto = (count)=>{
    const categories = [
        "DeFi",
        "Layer 1",
        "Layer 2",
        "Meme",
        "Gaming",
        "NFT",
        "Oracle",
        "Privacy",
        "Storage"
    ];
    const cryptoNames = [
        "ApeCoin",
        "Shiba Inu",
        "Cosmos",
        "Algorand",
        "VeChain",
        "Filecoin",
        "Sandbox",
        "Decentraland",
        "Axie Infinity",
        "Theta",
        "Hedera",
        "Elrond",
        "Near Protocol",
        "Flow",
        "Tezos",
        "Fantom",
        "Harmony",
        "Zilliqa",
        "Enjin",
        "Basic Attention Token",
        "Compound",
        "Maker",
        "Aave",
        "Curve",
        "SushiSwap",
        "PancakeSwap",
        "1inch",
        "Yearn Finance",
        "Synthetix",
        "Balancer",
        "Bancor",
        "Kyber Network",
        "0x Protocol"
    ];
    // Use a fixed seed for consistent results across server and client
    const rng = new SeededRandom(12345);
    return Array.from({
        length: count
    }, (_, i)=>{
        const basePrice = rng.next() * 1000 + 0.01;
        const marketCap = basePrice * (rng.next() * 1000000000 + 1000000);
        return {
            id: `crypto-${i + 16}`,
            name: cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`,
            symbol: `C${i + 16}`,
            price: Number(basePrice.toFixed(4)),
            marketCap: Number(marketCap.toFixed(0)),
            volume24h: Number((marketCap * (rng.next() * 0.3 + 0.05)).toFixed(0)),
            change24h: Number(((rng.next() - 0.5) * 20).toFixed(2)),
            change7d: Number(((rng.next() - 0.5) * 40).toFixed(2)),
            rank: i + 16,
            category: categories[Math.floor(rng.next() * categories.length)],
            description: `Description for ${cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`}`
        };
    });
};
const getAllCryptoData = ()=>{
    return [
        ...mockCryptoData,
        ...generateRandomCrypto(35)
    ];
};
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveBubbleChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ResponsiveBubbleChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SearchAndFilter$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/SearchAndFilter.tsx [app-ssr] (ecmascript)");
// CryptoDetailModal removed - details now show on hover
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockCryptoData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/mockCryptoData.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
function Home() {
    // Modal state removed - details now show on hover
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        sortBy: "marketCap",
        sortOrder: "desc"
    });
    const allCryptoData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockCryptoData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllCryptoData"])();
    // Simulate loading for demo purposes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const timer = setTimeout(()=>{
            setIsLoading(false);
        }, 1500);
        return ()=>clearTimeout(timer);
    }, []);
    // Get unique categories for filter dropdown
    const categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const uniqueCategories = [
            ...new Set(allCryptoData.map((crypto)=>crypto.category))
        ];
        return uniqueCategories.sort();
    }, [
        allCryptoData
    ]);
    // Filter and sort data
    const filteredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        let filtered = allCryptoData;
        // Apply search filter
        if (filters.searchTerm) {
            const searchLower = filters.searchTerm.toLowerCase();
            filtered = filtered.filter((crypto)=>crypto.name.toLowerCase().includes(searchLower) || crypto.symbol.toLowerCase().includes(searchLower));
        }
        // Apply category filter
        if (filters.category) {
            filtered = filtered.filter((crypto)=>crypto.category === filters.category);
        }
        // Apply sorting
        if (filters.sortBy) {
            filtered.sort((a, b)=>{
                const aValue = a[filters.sortBy];
                const bValue = b[filters.sortBy];
                if (typeof aValue === "string" && typeof bValue === "string") {
                    return filters.sortOrder === "desc" ? bValue.localeCompare(aValue) : aValue.localeCompare(bValue);
                }
                return filters.sortOrder === "desc" ? bValue - aValue : aValue - bValue;
            });
        }
        return filtered;
    }, [
        allCryptoData,
        filters
    ]);
    // Click handlers removed - details now show on hover
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-screen overflow-hidden",
        "data-oid": "nh6aeyy",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveBubbleChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ResponsiveBubbleChart"], {
                data: filteredData,
                isLoading: isLoading,
                "data-oid": "e1qxf3h"
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-4 left-4 z-50",
                "data-oid": "vkdbvt_",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30",
                    style: {
                        boxShadow: "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)"
                    },
                    "data-oid": "eymeu:z",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SearchAndFilter$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SearchAndFilter"], {
                        filters: filters,
                        onFiltersChange: setFilters,
                        categories: categories,
                        compact: true,
                        "data-oid": "h7iy:g:"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 93,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 92,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d1a4d3a5._.js.map